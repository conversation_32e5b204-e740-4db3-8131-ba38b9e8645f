# Task Master AI - Augment Integration Guide

## Essential Commands

```bash
# Project Setup
task-master init                                    # Initialize Task Master in current project
task-master parse-prd .taskmaster/docs/prd.txt      # Generate tasks from PRD document
task-master models --setup                          # Configure AI models interactively
```

## MCP Integration

Task Master provides an MCP server that Aug<PERSON> can connect to via the `.mcp.json` configuration.

## Important Files

- `.taskmaster/tasks/tasks.json` - Main task data file
- `.taskmaster/config.json` - AI model configuration
- `.mcp.json` - MCP server configuration for Augment
