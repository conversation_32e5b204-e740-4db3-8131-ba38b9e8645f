/**
 * Tests for the addSubtask function
 */
import { jest } from '@jest/globals';

// Mock dependencies using unstable_mockModule for ES modules
jest.unstable_mockModule('../../../../../scripts/modules/utils.js', () => ({
	log: jest.fn(),
	readJSON: jest.fn(),
	writeJSON: jest.fn()
}));

jest.unstable_mockModule('../../../../../scripts/modules/task-manager.js', () => ({
	isTaskDependentOn: jest.fn()
}));

jest.unstable_mockModule('../../../../../scripts/modules/task-manager/generate-task-files.js', () => ({
	default: jest.fn()
}));

jest.unstable_mockModule('path', () => ({
	default: {
		dirname: jest.fn().mockReturnValue('/mock/tasks')
	},
	dirname: jest.fn().mockReturnValue('/mock/tasks')
}));

// Import the mocked modules
const { log, readJSON, writeJSON } = await import('../../../../../scripts/modules/utils.js');
const { isTaskDependentOn } = await import('../../../../../scripts/modules/task-manager.js');
const generateTaskFiles = await import('../../../../../scripts/modules/task-manager/generate-task-files.js');

// Import the function to test
const addSubtask = (await import('../../../../../scripts/modules/task-manager/add-subtask.js')).default;

describe('addSubtask - New Tests', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		isTaskDependentOn.mockReturnValue(false);
		generateTaskFiles.default.mockResolvedValue();
	});

	const mockTasksData = {
		tasks: [
			{
				id: 1,
				title: 'Parent Task',
				description: 'A parent task',
				status: 'pending',
				dependencies: [],
				subtasks: []
			},
			{
				id: 2,
				title: 'Existing Task',
				description: 'An existing task to convert',
				status: 'pending',
				dependencies: []
			}
		]
	};

	describe('Creating new subtask', () => {
		it('should create a new subtask with provided data', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const newSubtaskData = {
				title: 'New Subtask',
				description: 'A new subtask',
				details: 'Detailed information',
				status: 'pending',
				dependencies: []
			};

			const result = await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(readJSON).toHaveBeenCalledWith('/mock/tasks.json');
			expect(writeJSON).toHaveBeenCalledWith('/mock/tasks.json', expect.objectContaining({
				tasks: expect.arrayContaining([
					expect.objectContaining({
						id: 1,
						subtasks: expect.arrayContaining([
							expect.objectContaining({
								id: 1,
								title: 'New Subtask',
								description: 'A new subtask',
								details: 'Detailed information',
								status: 'pending',
								dependencies: [],
								parentTaskId: 1
							})
						])
					})
				])
			}));
			expect(generateTaskFiles.default).toHaveBeenCalledWith('/mock/tasks.json', '/mock/tasks');
			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'New Subtask',
				parentTaskId: 1
			}));
		});

		it('should create subtask with default values when optional fields are missing', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const newSubtaskData = {
				title: 'Minimal Subtask'
			};

			const result = await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'Minimal Subtask',
				description: '',
				details: '',
				status: 'pending',
				dependencies: [],
				parentTaskId: 1
			}));
		});
	});

	describe('Converting existing task to subtask', () => {
		it('should convert an existing task to a subtask', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const result = await addSubtask('/mock/tasks.json', 1, 2);

			expect(writeJSON).toHaveBeenCalledWith('/mock/tasks.json', expect.objectContaining({
				tasks: expect.arrayContaining([
					expect.objectContaining({
						id: 1,
						subtasks: expect.arrayContaining([
							expect.objectContaining({
								id: 1,
								title: 'Existing Task',
								description: 'An existing task to convert',
								parentTaskId: 1
							})
						])
					})
				])
			}));
			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'Existing Task',
				parentTaskId: 1
			}));
		});
	});

	describe('Error handling', () => {
		it('should throw error when tasks file is invalid', async () => {
			readJSON.mockReturnValue(null);

			await expect(addSubtask('/mock/tasks.json', 1, null, { title: 'Test' }))
				.rejects.toThrow('Invalid or missing tasks file at /mock/tasks.json');
		});

		it('should throw error when parent task is not found', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await expect(addSubtask('/mock/tasks.json', 999, null, { title: 'Test' }))
				.rejects.toThrow('Parent task with ID 999 not found');
		});

		it('should throw error when trying to make task a subtask of itself', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await expect(addSubtask('/mock/tasks.json', 1, 1))
				.rejects.toThrow('Cannot make a task a subtask of itself');
		});

		it('should throw error when circular dependency would be created', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));
			isTaskDependentOn.mockReturnValue(true);

			await expect(addSubtask('/mock/tasks.json', 1, 2))
				.rejects.toThrow('Cannot create circular dependency: task 1 is already a subtask or dependent of task 2');
		});

		it('should throw error when neither existingTaskId nor newSubtaskData is provided', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await expect(addSubtask('/mock/tasks.json', 1, null, null))
				.rejects.toThrow('Either existingTaskId or newSubtaskData must be provided');
		});
	});

	describe('Configuration options', () => {
		it('should skip file generation when generateFiles is false', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData, false);

			expect(generateTaskFiles.default).not.toHaveBeenCalled();
		});

		it('should generate files by default', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(generateTaskFiles.default).toHaveBeenCalledWith('/mock/tasks.json', '/mock/tasks');
		});
	});

	describe('Logging', () => {
		it('should log appropriate messages during execution', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(log).toHaveBeenCalledWith('info', 'Adding subtask to parent task 1...');
			expect(log).toHaveBeenCalledWith('info', 'Created new subtask 1.1');
			expect(log).toHaveBeenCalledWith('info', 'Regenerating task files...');
		});

		it('should log error and rethrow when operation fails', async () => {
			readJSON.mockReturnValue(null);

			await expect(addSubtask('/mock/tasks.json', 1, null, { title: 'Test' }))
				.rejects.toThrow();

			expect(log).toHaveBeenCalledWith('error', expect.stringContaining('Error adding subtask:'));
		});
	});
});
