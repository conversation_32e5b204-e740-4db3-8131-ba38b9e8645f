/**
 * Comprehensive tests for the removeSubtask function
 */
import { jest } from '@jest/globals';

// Mock dependencies
const mockLog = jest.fn();
const mockReadJSON = jest.fn();
const mockWriteJSON = jest.fn();
const mockGenerateTaskFiles = jest.fn();

// Mock modules
jest.mock('../../../modules/utils.js', () => ({
	log: mockLog,
	readJSON: mockReadJSON,
	writeJSON: mockWriteJSON
}));

jest.mock('../../../modules/task-manager/generate-task-files.js', () => ({
	default: mockGenerateTaskFiles
}));

jest.mock('path', () => ({
	dirname: jest.fn().mockReturnValue('/mock/tasks')
}));

// Import the function to test
import removeSubtask from '../../../../../scripts/modules/task-manager/remove-subtask.js';

describe('removeSubtask - Comprehensive Tests', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockGenerateTaskFiles.mockResolvedValue();
	});

	const mockTasksData = {
		tasks: [
			{
				id: 1,
				title: 'Parent Task',
				description: 'A parent task',
				status: 'pending',
				dependencies: [],
				subtasks: [
					{
						id: 1,
						title: 'Subtask 1',
						description: 'First subtask',
						status: 'pending',
						dependencies: [],
						parentTaskId: 1
					},
					{
						id: 2,
						title: 'Subtask 2',
						description: 'Second subtask',
						status: 'completed',
						dependencies: [1],
						parentTaskId: 1
					}
				]
			}
		]
	};

	describe('Removing subtask without conversion', () => {
		it('should remove a subtask from its parent', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const result = await removeSubtask('/mock/tasks.json', '1.1');

			expect(mockWriteJSON).toHaveBeenCalledWith(
				'/mock/tasks.json',
				expect.objectContaining({
					tasks: expect.arrayContaining([
						expect.objectContaining({
							id: 1,
							subtasks: expect.arrayContaining([
								expect.objectContaining({
									id: 2,
									title: 'Subtask 2'
								})
							])
						})
					])
				})
			);
			expect(result).toBeNull();
		});

		it('should remove all references to the subtask from other subtasks dependencies', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			const writtenData = mockWriteJSON.mock.calls[0][1];
			const parentTask = writtenData.tasks.find((t) => t.id === 1);
			const remainingSubtask = parentTask.subtasks.find((st) => st.id === 2);

			// The dependency on subtask 1 should be removed
			expect(remainingSubtask.dependencies).not.toContain(1);
		});
	});

	describe('Converting subtask to standalone task', () => {
		it('should convert a subtask to a standalone task', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			const writtenData = mockWriteJSON.mock.calls[0][1];
			const newTask = writtenData.tasks.find((t) => t.id === 2); // Should get next available ID

			expect(newTask).toEqual(
				expect.objectContaining({
					id: 2,
					title: 'Subtask 1',
					description: 'First subtask',
					status: 'pending',
					dependencies: expect.arrayContaining([1]) // Should include parent as dependency
				})
			);
			expect(newTask.parentTaskId).toBeUndefined();
			expect(result).toEqual(newTask);
		});

		it('should add parent task as dependency when converting to standalone task', async () => {
			const dataWithoutParentDep = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: [
							{
								id: 1,
								title: 'Subtask 1',
								dependencies: [2], // Has other dependencies but not parent
								parentTaskId: 1
							}
						]
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithoutParentDep);

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			expect(result.dependencies).toContain(1);
			expect(result.dependencies).toContain(2);
		});

		it('should not duplicate parent dependency if already present', async () => {
			const dataWithParentDep = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: [
							{
								id: 1,
								title: 'Subtask 1',
								dependencies: [1], // Already depends on parent
								parentTaskId: 1
							}
						]
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithParentDep);

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			const parentDependencies = result.dependencies.filter((dep) => dep === 1);
			expect(parentDependencies).toHaveLength(1);
		});
	});

	describe('Error handling', () => {
		it('should throw error when tasks file is invalid', async () => {
			mockReadJSON.mockReturnValue(null);

			await expect(removeSubtask('/mock/tasks.json', '1.1')).rejects.toThrow(
				'Invalid or missing tasks file at /mock/tasks.json'
			);
		});

		it('should throw error when subtask ID format is invalid', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', 'invalid')).rejects.toThrow(
				'Invalid subtask ID format. Expected format: parentId.subtaskId'
			);
		});

		it('should throw error when parent task is not found', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', '999.1')).rejects.toThrow(
				'Parent task with ID 999 not found'
			);
		});

		it('should throw error when subtask is not found', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', '1.999')).rejects.toThrow(
				'Subtask with ID 999 not found in parent task 1'
			);
		});
	});

	describe('Configuration options', () => {
		it('should skip file generation when generateFiles is false', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1', false, false);

			expect(mockGenerateTaskFiles).not.toHaveBeenCalled();
		});

		it('should generate files by default', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			expect(mockGenerateTaskFiles).toHaveBeenCalledWith(
				'/mock/tasks.json',
				'/mock/tasks'
			);
		});
	});

	describe('Logging', () => {
		it('should log appropriate messages during execution', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			expect(mockLog).toHaveBeenCalledWith('info', 'Removing subtask 1.1...');
			expect(mockLog).toHaveBeenCalledWith('info', 'Subtask 1.1 deleted');
			expect(mockLog).toHaveBeenCalledWith('info', 'Regenerating task files...');
		});

		it('should log conversion message when converting to task', async () => {
			mockReadJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1', true);

			expect(mockLog).toHaveBeenCalledWith(
				'info',
				expect.stringMatching(/Created new task \d+ from subtask 1\.1/)
			);
		});

		it('should log error and rethrow when operation fails', async () => {
			mockReadJSON.mockReturnValue(null);

			await expect(removeSubtask('/mock/tasks.json', '1.1')).rejects.toThrow();

			expect(mockLog).toHaveBeenCalledWith(
				'error',
				expect.stringContaining('Error removing subtask:')
			);
		});
	});
});
