/**
 * Tests for the addSubtask function
 */
import { jest } from '@jest/globals';

// Mock dependencies using unstable_mockModule for ES modules
jest.unstable_mockModule('../../../../../scripts/modules/utils.js', () => ({
	log: jest.fn(),
	readJSON: jest.fn(),
	writeJSON: jest.fn()
}));

jest.unstable_mockModule('../../../../../scripts/modules/task-manager.js', () => ({
	isTaskDependentOn: jest.fn()
}));

jest.unstable_mockModule('../../../../../scripts/modules/task-manager/generate-task-files.js', () => ({
	default: jest.fn()
}));

jest.unstable_mockModule('path', () => ({
	dirname: jest.fn().mockReturnValue('/mock/tasks')
}));

// Import the mocked modules
const { log, readJ<PERSON><PERSON>, writeJ<PERSON><PERSON> } = await import('../../../../../scripts/modules/utils.js');
const { isTaskDependentOn } = await import('../../../../../scripts/modules/task-manager.js');
const generateTaskFiles = await import('../../../../../scripts/modules/task-manager/generate-task-files.js');

// Import the function to test
const { default: addSubtask } = await import('../../../../../scripts/modules/task-manager/add-subtask.js');

describe('addSubtask', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		isTaskDependentOn.mockReturnValue(false);
		generateTaskFiles.default.mockResolvedValue();
	});

	const mockTasksData = {
		tasks: [
			{
				id: 1,
				title: 'Parent Task',
				description: 'A parent task',
				status: 'pending',
				dependencies: [],
				subtasks: []
			},
			{
				id: 2,
				title: 'Existing Task',
				description: 'An existing task to convert',
				status: 'pending',
				dependencies: []
			}
		]
	};

	describe('Creating new subtask', () => {
		it('should create a new subtask with provided data', async () => {
			readJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = {
				title: 'New Subtask',
				description: 'A new subtask',
				details: 'Detailed information',
				status: 'pending',
				dependencies: []
			};

			const result = await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(readJSON).toHaveBeenCalledWith('/mock/tasks.json');
			expect(writeJSON).toHaveBeenCalledWith('/mock/tasks.json', expect.objectContaining({
				tasks: expect.arrayContaining([
					expect.objectContaining({
						id: 1,
						subtasks: expect.arrayContaining([
							expect.objectContaining({
								id: 1,
								title: 'New Subtask',
								description: 'A new subtask',
								details: 'Detailed information',
								status: 'pending',
								dependencies: [],
								parentTaskId: 1
							})
						])
					})
				])
			}));
			expect(generateTaskFiles.default).toHaveBeenCalledWith('/mock/tasks.json', '/mock/tasks');
			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'New Subtask',
				parentTaskId: 1
			}));
		});

		it('should create subtask with default values when optional fields are missing', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = {
				title: 'Minimal Subtask'
			};

			const result = await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'Minimal Subtask',
				description: '',
				details: '',
				status: 'pending',
				dependencies: [],
				parentTaskId: 1
			}));
		});

		it('should assign incremental IDs to multiple subtasks', async () => {
			const dataWithSubtasks = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: [
							{ id: 1, title: 'Existing Subtask 1', parentTaskId: 1 },
							{ id: 2, title: 'Existing Subtask 2', parentTaskId: 1 }
						]
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithSubtasks);

			const newSubtaskData = { title: 'Third Subtask' };
			const result = await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(result.id).toBe(3);
		});
	});

	describe('Converting existing task to subtask', () => {
		it('should convert an existing task to a subtask', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const result = await addSubtask('/mock/tasks.json', 1, 2);

			expect(mockWriteJSON).toHaveBeenCalledWith('/mock/tasks.json', expect.objectContaining({
				tasks: expect.arrayContaining([
					expect.objectContaining({
						id: 1,
						subtasks: expect.arrayContaining([
							expect.objectContaining({
								id: 1,
								title: 'Existing Task',
								description: 'An existing task to convert',
								parentTaskId: 1
							})
						])
					})
				])
			}));
			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'Existing Task',
				parentTaskId: 1
			}));
		});

		it('should remove the original task when converting to subtask', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await addSubtask('/mock/tasks.json', 1, 2);

			const writtenData = mockWriteJSON.mock.calls[0][1];
			const remainingTasks = writtenData.tasks.filter(t => t.id === 2);
			expect(remainingTasks).toHaveLength(0);
		});

		it('should preserve task properties when converting to subtask', async () => {
			const dataWithComplexTask = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: []
					},
					{
						id: 2,
						title: 'Complex Task',
						description: 'Complex description',
						details: 'Complex details',
						status: 'in-progress',
						dependencies: [3],
						priority: 'high'
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithComplexTask);

			const result = await addSubtask('/mock/tasks.json', 1, 2);

			expect(result).toEqual(expect.objectContaining({
				id: 1,
				title: 'Complex Task',
				description: 'Complex description',
				details: 'Complex details',
				status: 'in-progress',
				dependencies: [3],
				priority: 'high',
				parentTaskId: 1
			}));
		});
	});

	describe('Error handling', () => {
		it('should throw error when tasks file is invalid', async () => {
			mockReadJSON.mockReturnValue(null);

			await expect(addSubtask('/mock/tasks.json', 1, null, { title: 'Test' }))
				.rejects.toThrow('Invalid or missing tasks file at /mock/tasks.json');
		});

		it('should throw error when parent task is not found', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(addSubtask('/mock/tasks.json', 999, null, { title: 'Test' }))
				.rejects.toThrow('Parent task with ID 999 not found');
		});

		it('should throw error when existing task to convert is not found', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(addSubtask('/mock/tasks.json', 1, 999))
				.rejects.toThrow('Task with ID 999 not found');
		});

		it('should throw error when trying to convert task that is already a subtask', async () => {
			const dataWithSubtask = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: []
					},
					{
						id: 2,
						title: 'Already Subtask',
						parentTaskId: 1
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithSubtask);

			await expect(addSubtask('/mock/tasks.json', 1, 2))
				.rejects.toThrow('Task 2 is already a subtask of task 1');
		});

		it('should throw error when trying to make task a subtask of itself', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(addSubtask('/mock/tasks.json', 1, 1))
				.rejects.toThrow('Cannot make a task a subtask of itself');
		});

		it('should throw error when circular dependency would be created', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);
			mockIsTaskDependentOn.mockReturnValue(true);

			await expect(addSubtask('/mock/tasks.json', 1, 2))
				.rejects.toThrow('Cannot create circular dependency: task 1 is already a subtask or dependent of task 2');
		});

		it('should throw error when neither existingTaskId nor newSubtaskData is provided', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			await expect(addSubtask('/mock/tasks.json', 1, null, null))
				.rejects.toThrow('Either existingTaskId or newSubtaskData must be provided');
		});
	});

	describe('Configuration options', () => {
		it('should skip file generation when generateFiles is false', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData, false);

			expect(mockGenerateTaskFiles).not.toHaveBeenCalled();
		});

		it('should generate files by default', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(mockGenerateTaskFiles).toHaveBeenCalledWith('/mock/tasks.json', '/mock/tasks');
		});

		it('should initialize subtasks array if it does not exist', async () => {
			const dataWithoutSubtasks = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						// No subtasks property
					}
				]
			};
			mockReadJSON.mockReturnValue(dataWithoutSubtasks);

			const newSubtaskData = { title: 'First Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			const writtenData = mockWriteJSON.mock.calls[0][1];
			expect(writtenData.tasks[0].subtasks).toEqual([
				expect.objectContaining({
					id: 1,
					title: 'First Subtask',
					parentTaskId: 1
				})
			]);
		});

		it('should handle string parent IDs by converting to numbers', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = { title: 'Test Subtask' };
			const result = await addSubtask('/mock/tasks.json', '1', null, newSubtaskData);

			expect(result.parentTaskId).toBe(1);
		});

		it('should handle string existing task IDs by converting to numbers', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const result = await addSubtask('/mock/tasks.json', 1, '2');

			expect(result).toEqual(expect.objectContaining({
				title: 'Existing Task',
				parentTaskId: 1
			}));
		});
	});

	describe('Logging', () => {
		it('should log appropriate messages during execution', async () => {
			mockReadJSON.mockReturnValue(mockTasksData);

			const newSubtaskData = { title: 'Test Subtask' };
			await addSubtask('/mock/tasks.json', 1, null, newSubtaskData);

			expect(mockLog).toHaveBeenCalledWith('info', 'Adding subtask to parent task 1...');
			expect(mockLog).toHaveBeenCalledWith('info', 'Created new subtask 1.1');
			expect(mockLog).toHaveBeenCalledWith('info', 'Regenerating task files...');
		});

		it('should log error and rethrow when operation fails', async () => {
			mockReadJSON.mockReturnValue(null);

			await expect(addSubtask('/mock/tasks.json', 1, null, { title: 'Test' }))
				.rejects.toThrow();

			expect(mockLog).toHaveBeenCalledWith('error', expect.stringContaining('Error adding subtask:'));
		});
	});
});

	// Case 1: Convert an existing task to a subtask
	if (existingTaskId !== null) {
		const existingTaskIdNum = parseInt(existingTaskId, 10);

		// Find the existing task
		const existingTaskIndex = data.tasks.findIndex(
			(t) => t.id === existingTaskIdNum
		);
		if (existingTaskIndex === -1) {
			throw new Error(`Task with ID ${existingTaskIdNum} not found`);
		}

		const existingTask = data.tasks[existingTaskIndex];

		// Check if task is already a subtask
		if (existingTask.parentTaskId) {
			throw new Error(
				`Task ${existingTaskIdNum} is already a subtask of task ${existingTask.parentTaskId}`
			);
		}

		// Check for circular dependency
		if (existingTaskIdNum === parentIdNum) {
			throw new Error(`Cannot make a task a subtask of itself`);
		}

		// Check for circular dependency using mockIsTaskDependentOn
		if (mockIsTaskDependentOn()) {
			throw new Error(
				`Cannot create circular dependency: task ${parentIdNum} is already a subtask or dependent of task ${existingTaskIdNum}`
			);
		}

		// Find the highest subtask ID to determine the next ID
		const highestSubtaskId =
			parentTask.subtasks.length > 0
				? Math.max(...parentTask.subtasks.map((st) => st.id))
				: 0;
		const newSubtaskId = highestSubtaskId + 1;

		// Clone the existing task to be converted to a subtask
		newSubtask = {
			...existingTask,
			id: newSubtaskId,
			parentTaskId: parentIdNum
		};

		// Add to parent's subtasks
		parentTask.subtasks.push(newSubtask);

		// Remove the task from the main tasks array
		data.tasks.splice(existingTaskIndex, 1);
	}
	// Case 2: Create a new subtask
	else if (newSubtaskData) {
		// Find the highest subtask ID to determine the next ID
		const highestSubtaskId =
			parentTask.subtasks.length > 0
				? Math.max(...parentTask.subtasks.map((st) => st.id))
				: 0;
		const newSubtaskId = highestSubtaskId + 1;

		// Create the new subtask object
		newSubtask = {
			id: newSubtaskId,
			title: newSubtaskData.title,
			description: newSubtaskData.description || '',
			details: newSubtaskData.details || '',
			status: newSubtaskData.status || 'pending',
			dependencies: newSubtaskData.dependencies || [],
			parentTaskId: parentIdNum
		};

		// Add to parent's subtasks
		parentTask.subtasks.push(newSubtask);
	} else {
		throw new Error('Either existingTaskId or newSubtaskData must be provided');
	}

	// Write the updated tasks back to the file
	mockWriteJSON(tasksPath, data);

	// Generate task files if requested
	if (generateFiles) {
		mockGenerateTaskFiles(tasksPath, path.dirname(tasksPath));
	}

	return newSubtask;
};

describe('addSubtask function', () => {
	// Reset mocks before each test
	beforeEach(() => {
		jest.clearAllMocks();

		// Default mock implementations
		mockReadJSON.mockImplementation(() => ({
			tasks: [
				{
					id: 1,
					title: 'Parent Task',
					description: 'This is a parent task',
					status: 'pending',
					dependencies: []
				},
				{
					id: 2,
					title: 'Existing Task',
					description: 'This is an existing task',
					status: 'pending',
					dependencies: []
				},
				{
					id: 3,
					title: 'Another Task',
					description: 'This is another task',
					status: 'pending',
					dependencies: [1]
				}
			]
		}));

		// Setup success write response
		mockWriteJSON.mockImplementation((path, data) => {
			return data;
		});

		// Set up default behavior for dependency check
		mockIsTaskDependentOn.mockReturnValue(false);
	});

	test('should add a new subtask to a parent task', async () => {
		// Create new subtask data
		const newSubtaskData = {
			title: 'New Subtask',
			description: 'This is a new subtask',
			details: 'Implementation details for the subtask',
			status: 'pending',
			dependencies: []
		};

		// Execute the test version of addSubtask
		const newSubtask = testAddSubtask(
			'tasks/tasks.json',
			1,
			null,
			newSubtaskData,
			true
		);

		// Verify readJSON was called with the correct path
		expect(mockReadJSON).toHaveBeenCalledWith('tasks/tasks.json');

		// Verify writeJSON was called with the correct path
		expect(mockWriteJSON).toHaveBeenCalledWith(
			'tasks/tasks.json',
			expect.any(Object)
		);

		// Verify the subtask was created with correct data
		expect(newSubtask).toBeDefined();
		expect(newSubtask.id).toBe(1);
		expect(newSubtask.title).toBe('New Subtask');
		expect(newSubtask.parentTaskId).toBe(1);

		// Verify generateTaskFiles was called
		expect(mockGenerateTaskFiles).toHaveBeenCalled();
	});

	test('should convert an existing task to a subtask', async () => {
		// Execute the test version of addSubtask to convert task 2 to a subtask of task 1
		const convertedSubtask = testAddSubtask(
			'tasks/tasks.json',
			1,
			2,
			null,
			true
		);

		// Verify readJSON was called with the correct path
		expect(mockReadJSON).toHaveBeenCalledWith('tasks/tasks.json');

		// Verify writeJSON was called
		expect(mockWriteJSON).toHaveBeenCalled();

		// Verify the subtask was created with correct data
		expect(convertedSubtask).toBeDefined();
		expect(convertedSubtask.id).toBe(1);
		expect(convertedSubtask.title).toBe('Existing Task');
		expect(convertedSubtask.parentTaskId).toBe(1);

		// Verify generateTaskFiles was called
		expect(mockGenerateTaskFiles).toHaveBeenCalled();
	});

	test('should throw an error if parent task does not exist', async () => {
		// Create new subtask data
		const newSubtaskData = {
			title: 'New Subtask',
			description: 'This is a new subtask'
		};

		// Override mockReadJSON for this specific test case
		mockReadJSON.mockImplementationOnce(() => ({
			tasks: [
				{
					id: 1,
					title: 'Task 1',
					status: 'pending'
				}
			]
		}));

		// Expect an error when trying to add a subtask to a non-existent parent
		expect(() =>
			testAddSubtask('tasks/tasks.json', 999, null, newSubtaskData)
		).toThrow(/Parent task with ID 999 not found/);

		// Verify writeJSON was not called
		expect(mockWriteJSON).not.toHaveBeenCalled();
	});

	test('should throw an error if existing task does not exist', async () => {
		// Expect an error when trying to convert a non-existent task
		expect(() => testAddSubtask('tasks/tasks.json', 1, 999, null)).toThrow(
			/Task with ID 999 not found/
		);

		// Verify writeJSON was not called
		expect(mockWriteJSON).not.toHaveBeenCalled();
	});

	test('should throw an error if trying to create a circular dependency', async () => {
		// Force the isTaskDependentOn mock to return true for this test only
		mockIsTaskDependentOn.mockReturnValueOnce(true);

		// Expect an error when trying to create a circular dependency
		expect(() => testAddSubtask('tasks/tasks.json', 3, 1, null)).toThrow(
			/circular dependency/
		);

		// Verify writeJSON was not called
		expect(mockWriteJSON).not.toHaveBeenCalled();
	});

	test('should not regenerate task files if generateFiles is false', async () => {
		// Create new subtask data
		const newSubtaskData = {
			title: 'New Subtask',
			description: 'This is a new subtask'
		};

		// Execute the test version of addSubtask with generateFiles = false
		testAddSubtask('tasks/tasks.json', 1, null, newSubtaskData, false);

		// Verify writeJSON was called
		expect(mockWriteJSON).toHaveBeenCalled();

		// Verify task files were not regenerated
		expect(mockGenerateTaskFiles).not.toHaveBeenCalled();
	});
});
