# Task Master AI Testing Guide

This document outlines the testing approach for Task Master AI, including setup instructions, test organization, and best practices.

## Test Organization

- `tests/unit/`: Unit tests for individual components
- `tests/integration/`: Integration tests for module interactions
- `tests/e2e/`: End-to-end tests for complete workflows
- `tests/fixtures/`: Test fixtures and sample data

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with coverage reporting
npm run test:coverage

# Run specific test files or patterns
npm test -- tests/unit/scripts/modules/task-manager/analyze-task-complexity.test.js
npm test -- -t "Roo"  # Run tests containing "Roo" in their name
npm test -- --testPathPattern=ai-services  # Run tests in paths containing "ai-services"

# Run tests with reduced workers (for CI environments)
npm test -- --maxWorkers=4

# Run tests sequentially (for debugging)
npm test -- --runInBand
```

## Jest Configuration

Jest configuration is defined in `jest.config.js` in the project root:

```javascript
module.exports = {
  testEnvironment: 'node',
  transform: {},
  extensionsToTreatAsEsm: ['.js'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  testMatch: ['**/tests/**/*.test.js'],
  collectCoverageFrom: [
    'scripts/modules/**/*.js',
    'mcp-server/src/**/*.js',
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  verbose: true
};
```

## Mocking External Dependencies

### Mocking AI Services

```javascript
jest.unstable_mockModule(
  '../../../../../scripts/modules/ai-services-unified.js',
  () => ({
    generateObjectService: jest.fn().mockResolvedValue({
      mainResult: {
        tasks: []
      },
      telemetryData: {
        timestamp: new Date().toISOString(),
        userId: '**********',
        commandName: 'analyze-complexity',
        modelUsed: 'claude-3-5-sonnet',
        providerName: 'anthropic',
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        totalCost: 0.012414,
        currency: 'USD'
      }
    })
  })
);
```

### Mocking File System Operations

```javascript
// Mock file system operations
const mockReadJSON = jest.fn();
const mockWriteJSON = jest.fn();

jest.unstable_mockModule('../../scripts/modules/utils.js', () => ({
  readJSON: mockReadJSON,
  writeJSON: mockWriteJSON,
  // Other utility functions...
}));
```

## Example Unit Test Structure

```javascript
/**
 * Tests for the analyze_project_complexity MCP tool
 */

import { jest } from '@jest/globals';

// Mock dependencies
const mockAnalyzeTaskComplexityDirect = jest.fn();
jest.mock('../../../../mcp-server/src/core/task-master-core.js', () => ({
  analyzeTaskComplexityDirect: mockAnalyzeTaskComplexityDirect
}));

// Import module under test (after mocks)
const { default: analyzeComplexityTool } = await import(
  '../../../../mcp-server/src/tools/analyze-complexity.js'
);

describe('analyze_project_complexity tool', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('should call analyzeTaskComplexityDirect with correct parameters', async () => {
    // Test implementation
  });
});
```

## Best Practices for Writing Tests

1. **Follow the existing pattern**:
   - Place unit tests in `tests/unit/`
   - Place integration tests in `tests/integration/`
   - Mirror the directory structure of the source code

2. **Mock external dependencies**:
   - Always mock AI service calls
   - Mock file system operations
   - Use `jest.unstable_mockModule()` for ESM modules

3. **Test structure**:
   - Set up mocks BEFORE importing the module under test
   - Import the module under test
   - Use describe/test blocks for organization
   - Clear mocks in beforeEach

4. **Test both success and error cases**:
   - Test normal operation
   - Test error handling
   - Test edge cases

5. **For AI-related functionality**:
   - Mock AI responses with realistic data structures
   - Test prompt construction
   - Test response parsing

6. **For new commands**:
   - Test argument parsing
   - Test integration with other modules
   - Test output formatting

7. **Use test fixtures** in `tests/fixtures/` for sample data

## Testing AI-Generated Tests

When implementing the AI-powered test generation command (Task 24), follow these additional guidelines:

1. **Test the test generator itself**:
   - Mock AI responses with sample test content
   - Verify file generation works correctly
   - Test with different testing frameworks

2. **Validate generated tests**:
   - Ensure generated tests follow project conventions
   - Verify tests can be executed without errors
   - Check that generated tests provide meaningful coverage

3. **Framework-specific testing**:
   - Test with different testing frameworks (Jest, Mocha, etc.)
   - Verify framework-specific configuration is correctly applied