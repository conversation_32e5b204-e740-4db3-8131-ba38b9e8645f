{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": true, "exactOptionalPropertyTypes": false, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/scripts/*": ["./scripts/*"], "@/src/*": ["./src/*"], "@/mcp-server/*": ["./mcp-server/*"], "@/tests/*": ["./tests/*"]}, "types": ["node", "jest"], "lib": ["ES2022", "DOM"]}, "include": ["src/**/*", "scripts/**/*", "mcp-server/**/*", "*.js", "*.mjs", "*.ts"], "exclude": ["node_modules", "dist", "coverage", ".stryker-tmp", "tests/**/*", "**/*.min.js", "**/*.test.js", "**/*.spec.js"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}