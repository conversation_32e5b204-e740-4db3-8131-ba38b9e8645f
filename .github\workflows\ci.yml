name: CI

on:
  push:
    branches: [main, next, develop]
  pull_request:
    branches: [main, next, develop]

permissions:
  contents: read

jobs:
  setup:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install Dependencies
        id: install
        run: npm ci
        timeout-minutes: 2

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ matrix.node-version }}-modules-${{ hashFiles('**/package-lock.json') }}

  lint:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-20-modules-${{ hashFiles('**/package-lock.json') }}

      - name: Run ESLint
        run: npm run lint
        env:
          FORCE_COLOR: 1

  format-check:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-20-modules-${{ hashFiles('**/package-lock.json') }}

      - name: Format Check
        run: npm run format:check
        env:
          FORCE_COLOR: 1

  test:
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ matrix.node-version }}-modules-${{ hashFiles('**/package-lock.json') }}

      - name: Run Tests with Coverage
        run: npm run test:coverage
        env:
          NODE_ENV: test
          CI: true
          FORCE_COLOR: 1
        timeout-minutes: 10

      - name: Upload coverage to Codecov
        if: matrix.node-version == '20'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-node-${{ matrix.node-version }}
          path: |
            test-results
            coverage
            junit.xml
          retention-days: 30

  quality:
    needs: [lint, format-check, test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-20-modules-${{ hashFiles('**/package-lock.json') }}

      - name: Run Quality Checks
        run: npm run quality

      - name: Security Audit
        run: npm audit --audit-level=moderate
        continue-on-error: true
