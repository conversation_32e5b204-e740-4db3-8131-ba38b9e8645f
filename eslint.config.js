import js from '@eslint/js';
import prettier from 'eslint-config-prettier';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsparser from '@typescript-eslint/parser';

export default [
	js.configs.recommended,
	prettier,
	{
		files: ['**/*.{js,mjs}'],
		languageOptions: {
			ecmaVersion: 2024,
			sourceType: 'module',
			globals: {
				console: 'readonly',
				process: 'readonly',
				Buffer: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly',
				global: 'readonly',
				setTimeout: 'readonly',
				clearTimeout: 'readonly',
				setInterval: 'readonly',
				clearInterval: 'readonly'
			}
		},
		rules: {
			// Error prevention
			'no-console': 'off', // Allow console in CLI applications
			'no-debugger': 'error',
			'no-alert': 'error',

			// Modern JavaScript
			'prefer-const': 'warn',
			'no-var': 'error',
			'prefer-arrow-callback': 'off',
			'prefer-template': 'off',

			// Code quality
			'no-unused-vars': [
				'warn',
				{
					argsIgnorePattern: '^_',
					varsIgnorePattern: '^_'
				}
			],
			'no-duplicate-imports': 'warn',
			'no-unreachable': 'error',

			// Best practices
			eqeqeq: ['error', 'always'],
			curly: 'off', // Allow single-line if statements
			'no-eval': 'error',
			'no-implied-eval': 'error',

			// Style (handled by Prettier, but some logical rules)
			'no-multiple-empty-lines': ['error', { max: 2 }],
			'no-trailing-spaces': 'off' // Let Prettier handle this
		}
	},
	{
		files: ['tests/**/*.{js,mjs}', '**/*.test.{js,mjs}', '**/*.spec.{js,mjs}'],
		languageOptions: {
			globals: {
				describe: 'readonly',
				it: 'readonly',
				test: 'readonly',
				expect: 'readonly',
				beforeEach: 'readonly',
				afterEach: 'readonly',
				beforeAll: 'readonly',
				afterAll: 'readonly',
				jest: 'readonly'
			}
		},
		rules: {
			'no-console': 'off', // Allow console in tests
			'no-unused-vars': 'off' // Allow unused vars in tests for mocking
		}
	},
	{
		files: ['**/*.{ts,tsx}'],
		languageOptions: {
			parser: tsparser,
			parserOptions: {
				ecmaVersion: 2024,
				sourceType: 'module',
				project: './tsconfig.json'
			},
			globals: {
				console: 'readonly',
				process: 'readonly',
				Buffer: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly',
				global: 'readonly',
				setTimeout: 'readonly',
				clearTimeout: 'readonly',
				setInterval: 'readonly',
				clearInterval: 'readonly'
			}
		},
		plugins: {
			'@typescript-eslint': tseslint
		},
		rules: {
			// TypeScript-specific rules
			'@typescript-eslint/no-unused-vars': ['warn', {
				argsIgnorePattern: '^_',
				varsIgnorePattern: '^_'
			}],
			'@typescript-eslint/no-explicit-any': 'warn',
			'@typescript-eslint/explicit-function-return-type': 'off',
			'@typescript-eslint/explicit-module-boundary-types': 'off',
			'@typescript-eslint/no-inferrable-types': 'warn',
			'@typescript-eslint/prefer-const': 'error',

			// Disable base rules that are covered by TypeScript
			'no-unused-vars': 'off',
			'no-undef': 'off'
		}
	},
	{
		files: ['**/*.cjs'],
		languageOptions: {
			sourceType: 'script',
			globals: {
				module: 'readonly',
				exports: 'readonly',
				require: 'readonly',
				console: 'readonly',
				process: 'readonly',
				Buffer: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly'
			}
		}
	},
	{
		ignores: [
			'node_modules/**',
			'coverage/**',
			'dist/**',
			'.stryker-tmp/**',
			'*.min.js'
		]
	}
];
