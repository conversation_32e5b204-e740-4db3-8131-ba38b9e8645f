// @ts-check
/** @type {import('@stryker-mutator/api/core').PartialStrykerOptions} */
const config = {
	packageManager: 'npm',
	reporters: ['html', 'clear-text', 'progress'],
	testRunner: 'jest',
	testRunnerNodeArgs: ['--experimental-vm-modules'],
	coverageAnalysis: 'perTest',
	mutate: [
		'scripts/modules/**/*.js',
		'src/**/*.js',
		'mcp-server/src/**/*.js',
		'!**/*.test.js',
		'!**/*.spec.js',
		'!**/node_modules/**'
	],
	thresholds: {
		high: 80,
		low: 60,
		break: 50
	},
	timeoutMS: 60000,
	maxConcurrentTestRunners: 2
};

export default config;
